using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using virtu_manager_auth.Api.Presets.User;
using virtu_manager_auth.Controllers.Base;
using VirtuManager.Auth.Api.Api.Dto;
using virtu_manager_auth.Repositories;
using VirtuManager.Auth.Api.Services.Interfaces;
using VirtuManager.Auth.Application.Services;
using VirtuManager.Auth.Domain.Contracts;
using VirtuManager.Auth.Domain.Entities;
using VirtuManager.Auth.Domain.Enums;

namespace virtu_manager_auth.Controllers;

/*
 * TODO
 * Написать interceptor который будет перехватывать ошибки и возвращать нужный HTTP результат
 */

[ApiController]
[Route("/Api/[controller]")]
public class UsersController : BaseControllerWithRepository<UsersRepository, User>
{
    private readonly IHashDataService<BcryptAlgorithm> _hashDataService;

    private readonly IGetUsersService _getUsersService;

    private readonly ILogger<UsersController> _logger;

    public UsersController(UsersRepository usersRepository, IHashDataService<BcryptAlgorithm> hashDataService, ILogger<UsersController> logger,
        IGetUsersService getUsersService) : base(usersRepository)
    {
        ArgumentNullException.ThrowIfNull(hashDataService, nameof(hashDataService));
        ArgumentNullException.ThrowIfNull(logger, nameof(logger));
        ArgumentNullException.ThrowIfNull(getUsersService, nameof(getUsersService));

        _hashDataService = hashDataService;
        _logger = logger;
        _getUsersService = getUsersService;
    }

    [Obsolete]
    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet(nameof(GetUsers))]
    public async Task<IActionResult> GetUsers() => Ok(await _getUsersService.GetAll());

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet($"{nameof(GetUserById)}/{{id}}")]
    public async Task<IActionResult> GetUserById(Guid id)
    {
        var user = await _getUsersService.GetById(id);
        return Ok(user);
    }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet($"{nameof(GetUserByLogin)}/{{login}}")]
    public async Task<IActionResult> GetUserByLogin(string login)
    {
        var user = await _getUsersService.GetByLogin(login);
        return Ok(user);
    }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpDelete($"{nameof(DeleteUserById)}/{{id}}")]
    public async Task<IActionResult> DeleteUserById(Guid id)
    {
        var user = await Repository.GetUserById(id);
        if (user == null)
        {
            _logger.LogError("User with user id '{UserId}' not found", id);
            return NotFound(new UserNotFoundResponse());
        }

        var userLogin = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.Name);
        if (userLogin!.Value == user.Login)
        {
            _logger.LogError("User with login '{Login}' try to delete himself", userLogin);
            return Conflict(new UserNotDeleteYourselfResponse());
        }

        await Repository.Remove(user);
        _logger.LogInformation("Deleted user with login '{Login}'", user.Login);
        return Ok(new UserDeletedResponse());
    }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPost(nameof(AddUser))]
    public async Task<IActionResult> AddUser(CreateUserDto userDto)
    {
        var userByLoginExists = await Repository.GetUserByLogin(userDto.Login);
        if (userByLoginExists is not null)
        {
            _logger.LogError("User with login '{Login}' already exists", userByLoginExists);
            return Conflict(new UserExistsErrorResponse());
        }

        var hashedPassword = _hashDataService.HashData(userDto.Password);

        var user = new User
        {
            Login = userDto.Login,
            PasswordHash = hashedPassword,
            Email = userDto.Email,
            Role = userDto.Role ?? UserRole.Regular,
            CreatedAt = DateTime.Now
        };

        await Repository.Add(user);
        _logger.LogInformation("Added user with login '{Login}'", user.Login);
        return Ok(new UserAddedResponse());
    }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet(nameof(GetUsersPaged))]
    public async Task<IActionResult> GetUsersPaged(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        return Ok(await _getUsersService.GetPaged(pageNumber, pageSize));
    }
}