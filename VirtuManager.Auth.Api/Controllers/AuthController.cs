using Microsoft.AspNetCore.Mvc;
using VirtuManager.Auth.Api.Api.Dto;
using virtu_manager_auth.Api.Models;
using virtu_manager_auth.Api.Presets.Jwt;
using virtu_manager_auth.Api.Presets.User;
using virtu_manager_auth.Controllers.Base;
using virtu_manager_auth.Models;
using virtu_manager_auth.Repositories;
using VirtuManager.Auth.Api.Services.Interfaces;
using VirtuManager.Auth.Application.Services;
using VirtuManager.Auth.Domain.Contracts;
using VirtuManager.Auth.Domain.Entities;

namespace virtu_manager_auth.Controllers;

[ApiController]
[Route("/Api/[controller]")]
public class AuthController : BaseControllerWithRepository<UsersRepository, User>
{
    private readonly IHashDataService<BcryptAlgorithm> _hashDataService;

    private readonly IJwtService _jwtService;

    private readonly ILogger<AuthController> _logger;

    private readonly ICookieService _cookieService;

    public AuthController(UsersRepository repository, IHashDataService<BcryptAlgorithm> hashDataService, IJwtService jwtService,
        ILogger<AuthController> logger, ICookieService cookieService) : base(repository)
    {
        ArgumentNullException.ThrowIfNull(hashDataService);
        ArgumentNullException.ThrowIfNull(jwtService);
        ArgumentNullException.ThrowIfNull(logger);
        ArgumentNullException.ThrowIfNull(cookieService);

        _hashDataService = hashDataService;
        _jwtService = jwtService;
        _logger = logger;
        _cookieService = cookieService;
    }

    [HttpPost("Login")]
    public async Task<IActionResult> Login(LoginUserDto dto)
    {
        var user = await Repository.GetUserByLogin(dto.Login);
        if (user == null)
        {
            _logger.LogError("User with login '{Login}' not found", dto.Login);
            return NotFound(new UserNotFoundResponse());
        }

        var isValidPassword = _hashDataService.VerifyData(dto.Password, user.PasswordHash);

        if (!isValidPassword)
        {
            _logger.LogError("Invalid password for user with login '{Login}'", dto.Login);
            return Unauthorized("Invalid password");
        }

        var payload = new TokensPayload(user.Id, user.Login, user.Role);

        var tokens = new JwtTokens
        {
            AccessToken = _jwtService.GenerateAccessToken(payload),
            RefreshToken = _jwtService.GenerateRefreshToken(payload)
        };

        var hashedRefreshToken = _hashDataService.HashData(tokens.RefreshToken);

        user.RefreshTokenHash = hashedRefreshToken;
        user.UpdatedAt = DateTime.UtcNow;
        user.LastLoginAt = DateTime.UtcNow;
        await Repository.Update(user);

        _cookieService.SetRefreshTokenCookie(Response, tokens.RefreshToken);

        _logger.LogInformation("Authorized user with login '{Login}'", dto.Login);

        return Ok(new JwtTokenResponse(tokens.AccessToken));
    }

    [HttpPost("Refresh")]
    public async Task<IActionResult> Refresh()
    {
        var refreshToken = Request.Cookies["refreshToken"];
        if (string.IsNullOrWhiteSpace(refreshToken))
        {
            _logger.LogError("Refresh token is empty");
            return Unauthorized("Refresh token is empty");
        }

        var principal = _jwtService.ValidateToken(refreshToken);
        if (principal == null)
        {
            _logger.LogError("Invalid refresh token '{Token}'", refreshToken);
            return Unauthorized("Invalid refresh token");
        }

        var tokenType = principal.Claims.FirstOrDefault(c => c.Type == "tokenType")?.Value;
        if (tokenType != "refresh")
        {
            _logger.LogError("Invalid refresh token type '{TokenType}'", tokenType);
            return Unauthorized("Invalid token type");
        }

        var userIdString = principal.Claims.FirstOrDefault(c => c.Type == "userId")?.Value;
        if (!Guid.TryParse(userIdString, out var userId))
        {
            _logger.LogError("Invalid user id '{UserId}'", userIdString);
            return Unauthorized("Invalid user ID in token");
        }

        var user = await Repository.GetUserById(userId);
        if (user == null)
        {
            _logger.LogError("User with user id '{UserId}' not found", userId);
            return NotFound(new UserNotFoundResponse());
        }

        var isValidRefreshToken = _hashDataService.VerifyData(refreshToken, user.RefreshTokenHash!);
        if (!isValidRefreshToken)
        {
            _logger.LogError("Invalid refresh token for user with user id '{UserId}'", userId);
            return Unauthorized("Invalid refresh token");
        }

        var payload = new TokensPayload(user.Id, user.Login, user.Role);

        var tokens = new JwtTokens
        {
            AccessToken = _jwtService.GenerateAccessToken(payload),
            RefreshToken = _jwtService.GenerateRefreshToken(payload)
        };

        user.RefreshTokenHash = _hashDataService.HashData(tokens.RefreshToken);
        user.UpdatedAt = DateTime.UtcNow;
        await Repository.Update(user);

        _cookieService.SetRefreshTokenCookie(Response, tokens.RefreshToken);

        _logger.LogInformation("Refreshed tokens for user '{Login}'", user.Login);

        return Ok(new JwtTokenResponse(tokens.AccessToken));
    }

    [HttpPost("Logout")]
    public async Task<IActionResult> Logout()
    {
        var refreshToken = Request.Cookies["refreshToken"];
        if (string.IsNullOrWhiteSpace(refreshToken))
        {
            _logger.LogError("Refresh token is empty");
            return LogoutSuccessfully();
        }

        var principal = _jwtService.ValidateToken(refreshToken);
        if (principal == null)
        {
            _logger.LogError("Invalid refresh token for user with refresh token '{RefreshToken}'", refreshToken);
            return LogoutSuccessfully();
        }

        var userIdString = principal.Claims.FirstOrDefault(c => c.Type == "userId")?.Value;
        if (!Guid.TryParse(userIdString, out var userId))
        {
            _logger.LogError("Invalid user id '{UserId}'", userIdString);
            return LogoutSuccessfully();
        }
        
        var user = await Repository.GetUserById(userId);
        if (user == null)
        {
            _logger.LogError("User with user id '{UserId}' not found", userId);
            return LogoutSuccessfully();
        }

        user.RefreshTokenHash = null;
        user.UpdatedAt = DateTime.UtcNow;
        await Repository.Update(user);

        _logger.LogInformation("User '{Login}' logged out", user.Login);

        return LogoutSuccessfully();
    }
    
    private IActionResult LogoutSuccessfully()
    {
        Response.Cookies.Delete("refreshToken");
        return Ok(new UserLogout());
    }
}