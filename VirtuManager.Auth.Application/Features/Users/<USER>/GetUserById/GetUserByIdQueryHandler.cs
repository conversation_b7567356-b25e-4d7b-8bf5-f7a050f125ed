using FluentResults;
using MediatR;
using Microsoft.Extensions.Logging;
using VirtuManager.Auth.Application.ViewModels;
using VirtuManager.Auth.Domain.Repositories;

namespace VirtuManager.Auth.Application.Features.Users.Queries.GetUserById;

public sealed class GetUserByIdQueryHandler : IRequestHandler<GetUserByIdQuery, Result<UserViewModel>>
{
    private readonly IUsersRepository _repository;
    private readonly ILogger<GetUserByIdQueryHandler> _logger;
    
    public Task<Result<UserViewModel>> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }
}